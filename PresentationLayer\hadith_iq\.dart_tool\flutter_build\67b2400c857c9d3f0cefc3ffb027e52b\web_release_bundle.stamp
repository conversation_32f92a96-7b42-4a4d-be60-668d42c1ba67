{"inputs": ["D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\pubspec.yaml", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\.dart_tool\\flutter_build\\67b2400c857c9d3f0cefc3ffb027e52b\\main.dart.js", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\pubspec.yaml", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\assets\\images\\FYP_Logo.png", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\assets\\fonts\\HadithIQCustomIcons.ttf", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\assets\\fonts\\Amiri-Regular.ttf", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\assets\\fonts\\DejaVuSans.ttf", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\iconsax-0.0.8\\lib\\assets\\fonts\\iconsax.ttf", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_symbols_icons-4.2815.1\\lib\\fonts\\MaterialSymbolsOutlined.ttf", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_symbols_icons-4.2815.1\\lib\\fonts\\MaterialSymbolsRounded.ttf", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_symbols_icons-4.2815.1\\lib\\fonts\\MaterialSymbolsSharp.ttf", "C:\\Users\\<USER>\\Desktop\\flutter_windows_3.32.6-stable\\flutter\\bin\\cache\\artifacts\\material_fonts\\MaterialIcons-Regular.otf", "C:\\Users\\<USER>\\Desktop\\flutter_windows_3.32.6-stable\\flutter\\packages\\flutter\\lib\\src\\material\\shaders\\ink_sparkle.frag", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\archive-4.0.7\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\args-2.7.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.13.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\barcode-2.2.9\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\bidi-2.0.13\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\boolean_selector-2.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\chalkdart-3.0.5\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.4.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\clock-1.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cross_cache-1.0.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cross_file-0.3.4+2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\diffutil_dart-4.0.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dio-5.8.0+1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dio_web_adapter-2.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\equatable-2.0.7\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fake_async-1.3.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\ffi-2.1.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_picker-9.0.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fixnum-1.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fl_chart-1.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_chat_core-2.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_chat_types-3.6.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_chat_ui-2.1.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_lints-4.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_plugin_android_lifecycle-2.0.27\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\freezed_annotation-3.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\glob-2.1.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.3.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\iconsax-0.0.8\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\idb_shim-2.6.5+1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\image-4.5.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.20.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_annotation-4.9.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\leak_tracker-10.0.9\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\leak_tracker_flutter_testing-3.0.9\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\leak_tracker_testing-3.0.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\lints-4.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\matcher-0.12.17\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_symbols_icons-4.2815.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\meta-1.16.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\nested-1.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_parsing-1.1.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider-2.1.5\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_android-2.2.15\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_foundation-2.4.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_linux-2.2.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_platform_interface-2.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_windows-2.3.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\pdf-3.11.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\platform-3.1.6\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\plugin_platform_interface-2.1.8\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\popover-0.3.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\posix-6.0.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\provider-6.1.5\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\qr-3.0.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\screen_retriever-0.2.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\screen_retriever_linux-0.2.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\screen_retriever_macos-0.2.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\screen_retriever_platform_interface-0.2.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\screen_retriever_windows-0.2.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\scrollview_observer-1.26.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sembast-3.8.5\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sprintf-7.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stack_trace-1.12.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stream_channel-2.1.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.4.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\synchronized-3.3.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\term_glyph-1.2.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\test_api-0.7.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\typed_data-1.4.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\uuid-4.5.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\visibility_detector-0.4.0+2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vm_service-15.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web_socket-1.0.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web_socket_channel-3.0.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.11.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\window_manager-0.4.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xdg_directories-1.1.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\LICENSE", "C:\\Users\\<USER>\\Desktop\\flutter_windows_3.32.6-stable\\flutter\\bin\\cache\\pkg\\sky_engine\\LICENSE", "C:\\Users\\<USER>\\Desktop\\flutter_windows_3.32.6-stable\\flutter\\packages\\flutter\\LICENSE", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\web\\favicon.png", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\web\\icons\\Icon-192.png", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\web\\icons\\Icon-512.png", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\web\\icons\\Icon-maskable-192.png", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\web\\icons\\Icon-maskable-512.png", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\web\\index.html", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\web\\manifest.json"], "outputs": ["D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\main.dart.js", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\assets\\assets\\images\\FYP_Logo.png", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\assets\\assets\\fonts\\HadithIQCustomIcons.ttf", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\assets\\assets\\fonts\\Amiri-Regular.ttf", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\assets\\assets\\fonts\\DejaVuSans.ttf", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\assets\\packages\\iconsax\\lib\\assets\\fonts\\iconsax.ttf", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\assets\\packages\\material_symbols_icons\\lib\\fonts\\MaterialSymbolsOutlined.ttf", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\assets\\packages\\material_symbols_icons\\lib\\fonts\\MaterialSymbolsRounded.ttf", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\assets\\packages\\material_symbols_icons\\lib\\fonts\\MaterialSymbolsSharp.ttf", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\assets\\fonts\\MaterialIcons-Regular.otf", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\assets\\shaders\\ink_sparkle.frag", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\assets\\AssetManifest.json", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\assets\\AssetManifest.bin", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\assets\\AssetManifest.bin.json", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\assets\\FontManifest.json", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\assets\\NOTICES", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\favicon.png", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\icons\\Icon-192.png", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\icons\\Icon-512.png", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\icons\\Icon-maskable-192.png", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\icons\\Icon-maskable-512.png", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\manifest.json"]}