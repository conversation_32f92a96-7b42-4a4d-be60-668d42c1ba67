{"inputs": ["D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\assets\\AssetManifest.bin", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\assets\\AssetManifest.bin.json", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\assets\\AssetManifest.json", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\assets\\assets\\fonts\\Amiri-Regular.ttf", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\assets\\assets\\fonts\\DejaVuSans.ttf", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\assets\\assets\\fonts\\HadithIQCustomIcons.ttf", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\assets\\assets\\images\\FYP_Logo.png", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\assets\\FontManifest.json", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\assets\\fonts\\MaterialIcons-Regular.otf", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\assets\\NOTICES", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\assets\\packages\\iconsax\\lib\\assets\\fonts\\iconsax.ttf", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\assets\\packages\\material_symbols_icons\\lib\\fonts\\MaterialSymbolsOutlined.ttf", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\assets\\packages\\material_symbols_icons\\lib\\fonts\\MaterialSymbolsRounded.ttf", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\assets\\packages\\material_symbols_icons\\lib\\fonts\\MaterialSymbolsSharp.ttf", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\assets\\shaders\\ink_sparkle.frag", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\canvaskit\\canvaskit.js", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\canvaskit\\canvaskit.js.symbols", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\canvaskit\\canvaskit.wasm", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\canvaskit\\chromium\\canvaskit.js", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\canvaskit\\chromium\\canvaskit.js.symbols", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\canvaskit\\chromium\\canvaskit.wasm", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\canvaskit\\skwasm.js", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\canvaskit\\skwasm.js.symbols", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\canvaskit\\skwasm.wasm", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\favicon.png", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\flutter.js", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\flutter_bootstrap.js", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\icons\\Icon-192.png", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\icons\\Icon-512.png", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\icons\\Icon-maskable-192.png", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\icons\\Icon-maskable-512.png", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\index.html", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\main.dart.js", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\manifest.json", "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\version.json"], "outputs": ["D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\build\\web\\flutter_service_worker.js"]}