# Hadith IQ - Complete Setup Guide

## 🎯 Project Overview
Hadith IQ is an intelligent platform for Islamic researchers and scholars to semantically search, classify, and analyze Hadith data using NLP/ML techniques.

**Tech Stack:**
- Backend: Python (FastAPI)
- Frontend: Flutter Web/Desktop
- Database: PostgreSQL
- ML/NLP: HuggingFace Transformers, Sentence Transformers, FAISS

## 📋 Prerequisites

### System Requirements
- Python 3.9-3.10 (recommended for camel-tools compatibility)
- Flutter SDK 3.5.4+
- PostgreSQL database
- Git

## 🔧 Backend Setup

### 1. Python Environment
```bash
# Check Python version (should be 3.9-3.10 for best compatibility)
python --version

# Install dependencies
pip install -r requirements.txt
```

### 2. Database Configuration
Edit `config.properties`:
```ini
[database]
DB_URL = localhost
DB_PORT = 5432
DB_USER = your_postgres_user
DB_PASSWORD = your_postgres_password
DATABASE = hadith_iq

[API]
API_KEY = your_api_key_here
API_URL = your_api_url_here
```

### 3. Additional ML Dependencies
```bash
# Install camel-tools (may require Python 3.9-3.10)
pip install camel-tools

# Download NLTK data
python -c "import nltk; nltk.download('punkt')"
```

### 4. Start Backend Server
```bash
# From project root directory
uvicorn BusinessLogicLayer.server:app --host 127.0.0.1 --port 8000 --workers 4
```

## 💻 Frontend Setup

### 1. Install Flutter
- Download Flutter SDK from: https://flutter.dev/docs/get-started/install
- Add Flutter to your PATH
- Run `flutter doctor` to verify installation

### 2. Setup Flutter Project
```bash
cd PresentationLayer/hadith_iq
flutter clean
flutter pub get
flutter doctor
```

### 3. Enable Desktop Support (if needed)
```bash
flutter config --enable-windows-desktop
flutter config --enable-macos-desktop
flutter config --enable-linux-desktop
```

### 4. Run Flutter Application
```bash
# For Windows desktop
flutter run -d windows

# For web
flutter run -d chrome

# For development with hot reload
flutter run -d windows --hot
```

## 🗄️ Database Setup

### 1. Install PostgreSQL
- Download and install PostgreSQL
- Create a database named `hadith_iq`
- Create a user with appropriate permissions

### 2. Database Schema
The application will create tables automatically on first run, but ensure:
- Database exists
- User has CREATE, INSERT, UPDATE, DELETE permissions
- Connection details in `config.properties` are correct

## 🧪 Testing the Setup

### Backend Test
1. Start the backend server
2. Visit: http://127.0.0.1:8000/docs (FastAPI documentation)
3. Test the `/health` endpoint

### Frontend Test
1. Start the Flutter application
2. Verify it connects to the backend
3. Test basic navigation

## 🔍 Troubleshooting

### Common Issues

#### Backend Issues:
- **camel-tools compatibility**: Use Python 3.9-3.10
- **Database connection**: Check PostgreSQL service and credentials
- **Missing dependencies**: Run `pip install -r requirements.txt`

#### Frontend Issues:
- **Flutter not found**: Ensure Flutter is in PATH
- **Dependencies**: Run `flutter pub get`
- **Platform support**: Enable desktop support if needed

#### Database Issues:
- **Connection refused**: Check PostgreSQL service status
- **Authentication failed**: Verify credentials in config.properties
- **Database not found**: Create the database manually

## 📁 Project Structure
```
Hadith-IQ/
├── BusinessLogicLayer/          # Backend business logic
├── DataAccessLayer/             # Database access layer
├── PresentationLayer/hadith_iq/ # Flutter frontend
├── TO/                          # Transfer objects
├── services/                    # Additional services
├── config.properties           # Configuration file
├── requirements.txt            # Python dependencies
└── README.md                   # Project documentation
```

## 🚀 Development Workflow

1. **Start Backend**: `uvicorn BusinessLogicLayer.server:app --reload`
2. **Start Frontend**: `flutter run -d windows`
3. **Database**: Ensure PostgreSQL is running
4. **Development**: Use hot reload for both backend and frontend

## 📚 Additional Resources

- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [Flutter Documentation](https://flutter.dev/docs)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [Sentence Transformers](https://www.sbert.net/)

## 🆘 Support

If you encounter issues:
1. Check the troubleshooting section
2. Verify all prerequisites are installed
3. Check logs for specific error messages
4. Ensure all services (database, backend) are running
